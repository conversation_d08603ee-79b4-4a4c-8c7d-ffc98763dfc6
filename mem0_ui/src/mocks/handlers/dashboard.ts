import { http, HttpResponse } from 'msw';

// 支持多个API base URL
const API_BASES = [
  'http://localhost:8765/api/v1',
  'http://localhost:8000/v1',
  'http://localhost:8000',
  process.env.NEXT_PUBLIC_API_URL ? process.env.NEXT_PUBLIC_API_URL + '/v1' : null,
  process.env.NEXT_PUBLIC_MEM0_API_URL
].filter((url): url is string => Boolean(url));

// 真实统计数据（基于用户提供的实际数据）
const generateMockStats = (user_id?: string) => ({
  total_memories: 76,
  total_users: 33,
  search_events: 0,
  add_events: 46,
  graph_memories: 76, // 与total_memories保持一致
  entities_count: 150, // 估算值
  relationships_count: 200, // 估算值
  graph_density: 0.3, // 估算值
  last_updated: new Date().toISOString(),
  time_range: '24h'
});

// 模拟活动数据
const generateMockActivities = (user_id?: string, limit: number = 50) => {
  // 模拟真实数据分布：ADD操作占主导地位（80%），其他操作较少
  const operationWeights = [
    { operation: 'ADD', weight: 80, details: 'Created memory' },
    { operation: 'SEARCH', weight: 10, details: 'Memory search performed' },
    { operation: 'UPDATE', weight: 5, details: 'Updated memory' },
    { operation: 'DELETE', weight: 3, details: 'Deleted memory' },
    { operation: 'GRAPH_CREATE', weight: 2, details: 'Created graph memory' }
  ];

  // 创建加权操作数组
  const weightedOperations: Array<{ operation: string; details: string }> = [];
  operationWeights.forEach(({ operation, weight, details }) => {
    for (let i = 0; i < weight; i++) {
      weightedOperations.push({ operation, details });
    }
  });

  const statuses = ['success', 'error', 'pending'];

  const activities = [];
  for (let i = 0; i < limit; i++) {
    const { operation, details } = weightedOperations[Math.floor(Math.random() * weightedOperations.length)];
    const status = statuses[Math.floor(Math.random() * statuses.length)];

    activities.push({
      id: `activity_${Date.now()}_${i}`,
      timestamp: new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000).toISOString(),
      operation,
      details,
      response_time: `${Math.floor(Math.random() * 500) + 50}ms`,
      status,
      user_id: user_id || 'default',
      memory_id: `mem_${Math.random().toString(36).substr(2, 9)}`,
      metadata: {
        source: 'api',
        version: '1.0',
        categories: ['general', 'test']
      }
    });
  }

  return activities.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
};

// 模拟快速操作
const generateQuickActions = () => [
  {
    id: 'add_memory',
    label: '添加记忆',
    icon: 'plus',
    primary: true
  },
  {
    id: 'search_memories',
    label: '搜索记忆',
    icon: 'search',
    primary: false
  },
  {
    id: 'view_graph',
    label: '查看图谱',
    icon: 'network',
    primary: false
  },
  {
    id: 'export_data',
    label: '导出数据',
    icon: 'download',
    primary: false
  }
];

export const dashboardHandlers = API_BASES.flatMap(baseUrl => [
  // 获取统计数据 - GET /v1/stats
  http.get(`${baseUrl}/v1/stats`, ({ request }) => {
    const url = new URL(request.url);
    const user_id = url.searchParams.get('user_id');
    const time_range = url.searchParams.get('time_range') || '24h';
    
    console.log(`[MSW] GET ${baseUrl}/v1/stats - user_id: ${user_id}, time_range: ${time_range}`);
    
    const stats = generateMockStats(user_id || undefined);
    stats.time_range = time_range;
    
    return HttpResponse.json(stats);
  }),

  // 获取活动日志 - GET /v1/activities 和 /v1/activities/ (已禁用，使用真实API)
  /*
  http.get(`${baseUrl}/v1/activities`, ({ request }) => {
    const url = new URL(request.url);
    const user_id = url.searchParams.get('user_id');
    const limit = Math.min(parseInt(url.searchParams.get('limit') || '50'), 200); // 最大200
    const offset = parseInt(url.searchParams.get('offset') || '0');
    const operation_type = url.searchParams.get('operation_type');
    const start_time = url.searchParams.get('start_time');
    const end_time = url.searchParams.get('end_time');

    console.log(`[MSW] GET ${baseUrl}/v1/activities - user_id: ${user_id}, limit: ${limit}, offset: ${offset}`);

    let activities = generateMockActivities(user_id || undefined, limit + offset + 20);

    // 按操作类型过滤
    if (operation_type) {
      activities = activities.filter(activity => activity.operation === operation_type.toUpperCase());
    }

    // 按时间范围过滤
    if (start_time) {
      const startDate = new Date(start_time);
      activities = activities.filter(activity => new Date(activity.timestamp) >= startDate);
    }

    if (end_time) {
      const endDate = new Date(end_time);
      activities = activities.filter(activity => new Date(activity.timestamp) <= endDate);
    }

    // 分页
    const paginatedActivities = activities.slice(offset, offset + limit);

    return HttpResponse.json({
      activities: paginatedActivities,
      total: activities.length,
      has_more: offset + limit < activities.length,
      time_range: {
        start: start_time || new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
        end: end_time || new Date().toISOString()
      }
    });
  }),
  */

  // 获取活动日志 - GET /v1/activities/ (带尾部斜杠) (已禁用，使用真实API)
  /*
  http.get(`${baseUrl}/v1/activities/`, ({ request }) => {
    const url = new URL(request.url);
    const user_id = url.searchParams.get('user_id');
    const limit = Math.min(parseInt(url.searchParams.get('limit') || '50'), 200); // 最大200
    const offset = parseInt(url.searchParams.get('offset') || '0');
    const operation_type = url.searchParams.get('operation_type');
    const start_time = url.searchParams.get('start_time');
    const end_time = url.searchParams.get('end_time');

    console.log(`[MSW] GET ${baseUrl}/v1/activities/ - user_id: ${user_id}, limit: ${limit}, offset: ${offset}`);

    let activities = generateMockActivities(user_id || undefined, limit + offset + 20);

    // 按操作类型过滤
    if (operation_type) {
      activities = activities.filter(activity => activity.operation === operation_type);
    }

    // 按时间范围过滤
    if (start_time || end_time) {
      const startDate = start_time ? new Date(start_time) : new Date(0);
      const endDate = end_time ? new Date(end_time) : new Date();

      activities = activities.filter(activity => {
        const activityDate = new Date(activity.timestamp);
        return activityDate >= startDate && activityDate <= endDate;
      });
    }

    // 分页
    const paginatedActivities = activities.slice(offset, offset + limit);

    const result = {
      activities: paginatedActivities,
      total: activities.length,
      limit,
      offset,
      has_more: offset + limit < activities.length,
      time_range: {
        start: start_time || new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
        end: end_time || new Date().toISOString()
      }
    };

    return HttpResponse.json(result);
  }),
  */

  // 获取管理面板数据 - GET /v1/admin/dashboard
  http.get(`${baseUrl}/v1/admin/dashboard`, ({ request }) => {
    const url = new URL(request.url);
    const user_id = url.searchParams.get('user_id');
    const time_range = url.searchParams.get('time_range') || '24h';
    
    console.log(`[MSW] GET ${baseUrl}/v1/admin/dashboard - user_id: ${user_id}, time_range: ${time_range}`);
    
    const stats = generateMockStats(user_id || undefined);
    const recentActivities = generateMockActivities(user_id || undefined, 10);
    const quickActions = generateQuickActions();
    
    return HttpResponse.json({
      stats,
      recent_activities: recentActivities,
      quick_actions: quickActions,
      last_updated: new Date().toISOString()
    });
  })
]);
